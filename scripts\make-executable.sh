#!/bin/bash

# Make all scripts executable
# Run this script to set proper permissions on all shell scripts

echo "🔧 Setting executable permissions on scripts..."

# Make this script executable first
chmod +x scripts/make-executable.sh

# Make all shell scripts executable
chmod +x scripts/swarm-init.sh
chmod +x scripts/swarm-deploy.sh
chmod +x scripts/build-images.sh

# Make AWS config script executable
chmod +x config/load-aws-config.sh

echo "✅ All scripts are now executable!"
echo ""
echo "Available scripts:"
echo "• scripts/swarm-init.sh     - Initialize Docker Swarm on EC2"
echo "• scripts/swarm-deploy.sh   - Deploy/update the application stack"
echo "• scripts/build-images.sh   - Build and push Docker images"
echo "• config/load-aws-config.sh - Load AWS configuration"
