#!/bin/bash

# ChanguApp Docker Swarm Initialization Script
# This script sets up Docker Swarm on your EC2 instance
# Run this ONCE on your EC2 instance to initialize Swarm mode

set -e

echo "🚀 ChanguApp Docker Swarm Initialization"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. Consider using a non-root user with docker group membership."
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

print_status "Checking Docker service status..."
if ! systemctl is-active --quiet docker; then
    print_status "Starting Docker service..."
    sudo systemctl start docker
    sudo systemctl enable docker
fi

# Check if already in swarm mode
if docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
    print_warning "Docker Swarm is already initialized."
    docker node ls
else
    print_status "Initializing Docker Swarm..."
    
    # Get the private IP of the EC2 instance
    PRIVATE_IP=$(curl -s http://***************/latest/meta-data/local-ipv4)
    print_status "Using private IP: $PRIVATE_IP"
    
    # Initialize Docker Swarm
    docker swarm init --advertise-addr $PRIVATE_IP
    print_success "Docker Swarm initialized successfully!"
fi

# Create Docker Swarm secrets
print_status "Creating Docker Swarm secrets..."

# Generate secure passwords if they don't exist
POSTGRES_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)

# Create secrets (will fail silently if they already exist)
echo "$POSTGRES_PASSWORD" | docker secret create changuapp_postgres_password - 2>/dev/null || print_warning "Postgres password secret already exists"
echo "$JWT_SECRET" | docker secret create changuapp_jwt_secret - 2>/dev/null || print_warning "JWT secret already exists"

print_success "Secrets created successfully!"

# Create Docker Swarm configs
print_status "Creating Docker Swarm configs..."

# Create nginx config (will fail silently if it already exists)
docker config create changuapp_nginx_config /home/<USER>/tuchanga/config/nginx-proxy.conf 2>/dev/null || print_warning "Nginx config already exists"

print_success "Configs created successfully!"

# Create overlay network
print_status "Creating overlay network..."
docker network create --driver overlay --attachable changuapp-network 2>/dev/null || print_warning "Network already exists"

print_success "Network created successfully!"

# Show current swarm status
print_status "Current Docker Swarm status:"
echo ""
docker node ls
echo ""
docker secret ls
echo ""
docker config ls
echo ""
docker network ls --filter driver=overlay

print_success "Docker Swarm initialization completed!"
echo ""
print_status "Next steps:"
echo "1. Build and push your Docker images to a registry"
echo "2. Deploy the stack with: docker stack deploy -c docker-stack.yml changuapp"
echo "3. Monitor with: docker service ls"
echo ""
print_status "Useful commands:"
echo "• Check services: docker service ls"
echo "• Check service logs: docker service logs changuapp_backend"
echo "• Scale service: docker service scale changuapp_backend=2"
echo "• Update service: docker service update changuapp_backend"
echo "• Remove stack: docker stack rm changuapp"
echo ""
print_success "Setup complete! 🎉"
