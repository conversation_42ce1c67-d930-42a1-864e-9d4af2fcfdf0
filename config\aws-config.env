# ChanguApp AWS Configuration
# ===========================
# Central configuration file for all AWS EC2 instance details
# This file should be sourced by all deployment scripts and pipelines

# EC2 Instance Configuration
EC2_HOST="ec2-18-231-29-251.sa-east-1.compute.amazonaws.com"
EC2_PUBLIC_IP="*************"
EC2_USER="ec2-user"
EC2_REGION="sa-east-1"
EC2_INSTANCE_TYPE="t3.micro"

# SSH Configuration
SSH_KEY_PATH="./backend/keys/tuchanga.pem"
SSH_KEY_NAME="tuchanga"

# Application Paths
APP_HOME_DIR="/home/<USER>"
APP_DEPLOY_DIR="/home/<USER>/tuchanga"
APP_WEB_DIR="/var/www/tuchanga"
APP_ENV_FILE="/home/<USER>/tuchanga-backend.env"

# Service Ports
FRONTEND_PORT="80"
BACKEND_PORT="3000"
POSTGRES_PORT="5432"
JENKINS_PORT="8080"

# Service URLs
FRONTEND_URL="http://${EC2_PUBLIC_IP}/"
BACKEND_URL="http://${EC2_PUBLIC_IP}:${BACKEND_PORT}/"
HEALTH_URL="http://${EC2_PUBLIC_IP}:${BACKEND_PORT}/health"
JENKINS_URL="http://${EC2_PUBLIC_IP}:${JENKINS_PORT}/"

# AWS Configuration
AWS_REGION="sa-east-1"
AWS_AMI_ID="ami-0c02fb55956c7d316"  # Amazon Linux 2023 AMI for sa-east-1
AWS_SECURITY_GROUP_NAME="changuapp-sg"

# Database Configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_USERNAME="changuapp"
DB_PASSWORD="ChanguApp2024!SecureDB"
DB_DATABASE="job_platform"

# Jenkins Configuration (if used)
JENKINS_USER="admin"
JENKINS_TOKEN="11f4cbb243f4af1c816a2fa07c0aaca7cf"

# Application Configuration
APP_NAME="ChanguApp"
PROJECT_NAME="ChanguApp"
ENVIRONMENT="production"

# Backup Configuration
BACKUP_DIR="/opt/changuapp/backups"
BACKUP_RETENTION_DAYS="7"

# Monitoring URLs
HEALTH_CHECK_ENDPOINT="/health"
METRICS_ENDPOINT="/metrics"

# Deployment Configuration
DEPLOYMENT_TIMEOUT="600"  # seconds
HEALTH_CHECK_TIMEOUT="60"  # seconds
HEALTH_CHECK_RETRIES="5"

# Nginx Configuration
NGINX_CONFIG_PATH="/etc/nginx/conf.d/changuapp.conf"
NGINX_WEB_ROOT="/var/www/tuchanga/frontend"

# PM2 Configuration
PM2_APP_NAME="changuapp-backend"
PM2_ECOSYSTEM_FILE="ecosystem.config.js"

# Docker Configuration
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
DOCKER_NETWORK="changuapp-network-prod"

# Log Paths
LOG_DIR="/opt/changuapp/logs"
NGINX_LOG_DIR="/var/log/nginx"
POSTGRES_LOG_DIR="/var/lib/pgsql/15/data/log"

# SSL Configuration (if needed)
SSL_CERT_PATH="/etc/ssl/certs"
SSL_KEY_PATH="/etc/ssl/private"
DOMAIN_NAME=""  # Set if using custom domain

# Notification Configuration (optional)
WEBHOOK_URL=""  # Set if using webhooks for notifications
SLACK_WEBHOOK=""  # Set if using Slack notifications

# Development/Testing Configuration
DEV_FRONTEND_PORT="5173"
DEV_BACKEND_PORT="3000"
TEST_DATABASE="job_platform_test"

# Export all variables for use in scripts
export EC2_HOST EC2_PUBLIC_IP EC2_USER EC2_REGION EC2_INSTANCE_TYPE
export SSH_KEY_PATH SSH_KEY_NAME
export APP_HOME_DIR APP_DEPLOY_DIR APP_WEB_DIR APP_ENV_FILE
export FRONTEND_PORT BACKEND_PORT POSTGRES_PORT JENKINS_PORT
export FRONTEND_URL BACKEND_URL HEALTH_URL JENKINS_URL
export AWS_REGION AWS_AMI_ID AWS_SECURITY_GROUP_NAME
export DB_HOST DB_PORT DB_USERNAME DB_PASSWORD DB_DATABASE
export JENKINS_USER JENKINS_TOKEN
export APP_NAME PROJECT_NAME ENVIRONMENT
export BACKUP_DIR BACKUP_RETENTION_DAYS
export HEALTH_CHECK_ENDPOINT METRICS_ENDPOINT
export DEPLOYMENT_TIMEOUT HEALTH_CHECK_TIMEOUT HEALTH_CHECK_RETRIES
export NGINX_CONFIG_PATH NGINX_WEB_ROOT
export PM2_APP_NAME PM2_ECOSYSTEM_FILE
export DOCKER_COMPOSE_FILE DOCKER_NETWORK
export LOG_DIR NGINX_LOG_DIR POSTGRES_LOG_DIR
export SSL_CERT_PATH SSL_KEY_PATH DOMAIN_NAME
export WEBHOOK_URL SLACK_WEBHOOK
export DEV_FRONTEND_PORT DEV_BACKEND_PORT TEST_DATABASE
