# ChanguApp Docker Swarm Deployment Guide

This guide explains how to deploy ChanguApp using Docker Swarm instead of SSH-based deployments, eliminating connection issues and providing better reliability.

## 🎯 Benefits of Docker Swarm

- **No SSH timeouts**: Deployments happen via Docker API, not SSH
- **Automatic health checks**: Services restart automatically if they fail
- **Rolling updates**: Zero-downtime deployments
- **Better resource management**: Memory and CPU limits
- **Centralized logging**: Easy access to all service logs
- **Secrets management**: Secure handling of passwords and tokens

## 📋 Prerequisites

1. **AWS EC2 instance** (t3.micro free tier works perfectly)
2. **Docker Hub account** for image registry
3. **GitHub Secrets** configured:
   - `EC2_SSH_KEY`: Your EC2 private key
   - `DOCKER_HUB_USERNAME`: Your Docker Hub username
   - `DOCKER_HUB_TOKEN`: Your Docker Hub access token

## 🚀 Initial Setup (One-time)

### Step 1: Make Scripts Executable
```bash
# On your local machine
chmod +x scripts/make-executable.sh
./scripts/make-executable.sh
```

### Step 2: Initialize Docker Swarm on EC2
```bash
# SSH to your EC2 instance
ssh -i your-key.pem ec2-user@your-ec2-host

# Copy the initialization script
scp -i your-key.pem scripts/swarm-init.sh ec2-user@your-ec2-host:/home/<USER>/
scp -i your-key.pem config/nginx-proxy.conf ec2-user@your-ec2-host:/home/<USER>/tuchanga/config/

# Run the initialization script
chmod +x /home/<USER>/swarm-init.sh
./swarm-init.sh
```

This script will:
- Initialize Docker Swarm
- Create secure secrets for database and JWT
- Set up overlay networks
- Create nginx configuration

### Step 3: Set up Docker Hub Secrets in GitHub

1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Add these secrets:
   - `DOCKER_HUB_USERNAME`: Your Docker Hub username
   - `DOCKER_HUB_TOKEN`: Generate from Docker Hub → Account Settings → Security

## 🔄 Deployment Process

### Automatic Deployment (Recommended)

1. **Commit your changes** with deployment flags:
   ```bash
   git commit -m "Update backend --backend-deploy"
   git commit -m "Update frontend --frontend-deploy"
   git commit -m "Update both --frontend-deploy --backend-deploy"
   ```

2. **Push to main branch**:
   ```bash
   git push origin main
   ```

3. **GitHub Actions will**:
   - Build Docker images
   - Push to Docker Hub
   - Deploy to your EC2 Swarm cluster
   - Verify deployment health

### Manual Deployment

If you prefer manual control:

```bash
# Build and push images
./scripts/build-images.sh all latest

# Deploy to swarm
./scripts/swarm-deploy.sh all latest
```

## 📊 Monitoring and Management

### Check Service Status
```bash
# On your EC2 instance
docker service ls
docker stack ps changuapp
```

### View Service Logs
```bash
# Backend logs
docker service logs changuapp_backend --follow

# Frontend logs
docker service logs changuapp_frontend --follow

# Database logs
docker service logs changuapp_postgres --follow
```

### Scale Services
```bash
# Scale backend to 2 replicas
docker service scale changuapp_backend=2

# Scale back to 1
docker service scale changuapp_backend=1
```

### Update a Service
```bash
# Update backend with new image
docker service update changuapp_backend --image changuapp/backend:new-tag

# Update with rollback capability
docker service update changuapp_backend --image changuapp/backend:new-tag --update-failure-action rollback
```

## 🔧 Configuration Files

### Key Files
- `docker-stack.yml`: Main Swarm stack definition
- `config/nginx-proxy.conf`: Nginx reverse proxy configuration
- `scripts/swarm-init.sh`: One-time Swarm initialization
- `scripts/swarm-deploy.sh`: Deployment script
- `.github/workflows/swarm-deploy.yml`: GitHub Actions workflow

### Environment Variables
The stack uses Docker Swarm secrets for sensitive data:
- `changuapp_postgres_password`: Database password
- `changuapp_jwt_secret`: JWT signing secret

## 🌐 Service Access

After deployment, your services are available at:

- **Frontend**: `http://your-ec2-ip/`
- **Backend API**: `http://your-ec2-ip:8080/api/`
- **Health Check**: `http://your-ec2-ip:8080/health`

## 🛠️ Troubleshooting

### Services Not Starting
```bash
# Check service status
docker service ls

# Check service events
docker service ps changuapp_backend --no-trunc

# Check logs
docker service logs changuapp_backend --tail 50
```

### Rollback Failed Deployment
```bash
# Rollback to previous version
docker service rollback changuapp_backend

# Or rollback entire stack
docker stack rm changuapp
docker stack deploy -c docker-stack.yml changuapp
```

### Clean Up Resources
```bash
# Remove the entire stack
docker stack rm changuapp

# Clean up unused images
docker system prune -f

# Clean up unused volumes
docker volume prune -f
```

### Reset Swarm (if needed)
```bash
# Leave swarm mode
docker swarm leave --force

# Re-initialize
./scripts/swarm-init.sh
```

## 🔄 Migration from Old Deployment

If you're migrating from the SSH-based deployment:

1. **Stop old services**:
   ```bash
   docker-compose down
   sudo systemctl stop nginx
   ```

2. **Initialize Swarm** (follow setup steps above)

3. **Deploy with Swarm**:
   ```bash
   ./scripts/swarm-deploy.sh all latest
   ```

4. **Update GitHub workflow**: Use `.github/workflows/swarm-deploy.yml`

## 📈 Performance Benefits

- **Faster deployments**: No SSH connection overhead
- **Better reliability**: Automatic service recovery
- **Resource efficiency**: Proper memory/CPU limits
- **Zero downtime**: Rolling updates
- **Better monitoring**: Centralized service status

## 🔒 Security Features

- **Secrets management**: Passwords stored securely in Swarm
- **Network isolation**: Services communicate via overlay network
- **Resource limits**: Prevents resource exhaustion
- **Health checks**: Automatic unhealthy service replacement

## 📞 Support

If you encounter issues:

1. Check service logs: `docker service logs changuapp_backend`
2. Verify service status: `docker service ls`
3. Check GitHub Actions logs for build/deployment issues
4. Ensure Docker Hub credentials are correct

The Docker Swarm setup provides a much more reliable and maintainable deployment process compared to SSH-based deployments!
