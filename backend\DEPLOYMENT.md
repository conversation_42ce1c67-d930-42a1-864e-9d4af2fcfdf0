# ChanguApp Backend Deployment Guide

## 🚀 Production Deployment

The backend uses a **systemd service** instead of PM2 for better reliability and easier management.

### Automatic Deployment (GitHub Actions)

The deployment happens automatically when you push with `--backend-deploy` in the commit message:

```bash
git commit -m "Update backend --backend-deploy"
git push
```

### Manual Deployment

If you need to deploy manually:

```bash
# 1. SSH to the server
ssh -i backend/keys/tuchanga.pem <EMAIL>

# 2. Navigate to the backend directory
cd /home/<USER>/tuchanga/backend

# 3. Install dependencies and build
npm ci
npm run build

# 4. Restart the service
sudo systemctl restart changuapp-backend
```

## 🔧 Service Management

### Check Service Status
```bash
sudo systemctl status changuapp-backend
```

### View Logs
```bash
# Real-time logs
sudo journalctl -u changuapp-backend -f

# Last 50 lines
sudo journalctl -u changuapp-backend -n 50
```

### Start/Stop/Restart Service
```bash
sudo systemctl start changuapp-backend
sudo systemctl stop changuapp-backend
sudo systemctl restart changuapp-backend
```

### Health Check
```bash
cd /home/<USER>/tuchanga/backend
npm run health
```

## 🛠️ Local Production Testing

You can test the production setup locally:

```bash
# Build the application
npm run build

# Start with production script
npm run start:production
```

## 📁 File Structure

- `start-production.js` - Production server starter with auto-restart
- `health-check.js` - Health check script for monitoring
- `/etc/systemd/system/changuapp-backend.service` - Systemd service file

## 🔍 Troubleshooting

### Service Won't Start
1. Check if the build exists: `ls -la dist/src/main.js`
2. Check service logs: `sudo journalctl -u changuapp-backend -n 20`
3. Verify environment file: `ls -la /home/<USER>/tuchanga-backend.env`

### Database Connection Issues
1. Check PostgreSQL status: `sudo systemctl status postgresql`
2. Test database connection manually
3. Verify environment variables in the service

### Port Already in Use
```bash
# Find what's using port 3000
sudo lsof -i :3000

# Kill the process if needed
sudo kill -9 <PID>
```

## 🌐 URLs

- **Frontend**: http://ec2-54-233-23-129.sa-east-1.compute.amazonaws.com/
- **Backend API**: http://ec2-54-233-23-129.sa-east-1.compute.amazonaws.com:3000/
- **API Docs**: http://ec2-54-233-23-129.sa-east-1.compute.amazonaws.com:3000/api/docs
- **Health Check**: http://ec2-54-233-23-129.sa-east-1.compute.amazonaws.com:3000/health
