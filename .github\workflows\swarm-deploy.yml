name: <PERSON><PERSON><PERSON><PERSON> Docker Swarm Deployment

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

# This workflow uses Docker Swarm for zero-SSH deployments
# Configuration is loaded dynamically from config/aws-config.env

env:
  DOCKER_REGISTRY: changuapp
  IMAGE_TAG: ${{ github.sha }}

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      deploy-frontend: ${{ steps.check.outputs.deploy-frontend }}
      deploy-backend: ${{ steps.check.outputs.deploy-backend }}
      should-deploy: ${{ steps.check.outputs.should-deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check deployment flags
        id: check
        run: |
          COMMIT_MESSAGE="${{ github.event.head_commit.message }}"
          SHOULD_DEPLOY=false

          if [[ "$COMMIT_MESSAGE" == *"--frontend-deploy"* ]]; then
            echo "deploy-frontend=true" >> $GITHUB_OUTPUT
            echo "🎨 Frontend deployment requested"
            SHOULD_DEPLOY=true
          else
            echo "deploy-frontend=false" >> $GITHUB_OUTPUT
          fi

          if [[ "$COMMIT_MESSAGE" == *"--backend-deploy"* ]]; then
            echo "deploy-backend=true" >> $GITHUB_OUTPUT
            echo "🔧 Backend deployment requested"
            SHOULD_DEPLOY=true
          else
            echo "deploy-backend=false" >> $GITHUB_OUTPUT
          fi

          echo "should-deploy=$SHOULD_DEPLOY" >> $GITHUB_OUTPUT

  build-images:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.should-deploy == 'true'
    outputs:
      backend-image: ${{ steps.build.outputs.backend-image }}
      frontend-image: ${{ steps.build.outputs.frontend-image }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Build and push backend image
        if: needs.detect-changes.outputs.deploy-backend == 'true'
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ env.DOCKER_REGISTRY }}/backend:${{ env.IMAGE_TAG }}
            ${{ env.DOCKER_REGISTRY }}/backend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push frontend image
        if: needs.detect-changes.outputs.deploy-frontend == 'true'
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: |
            ${{ env.DOCKER_REGISTRY }}/frontend:${{ env.IMAGE_TAG }}
            ${{ env.DOCKER_REGISTRY }}/frontend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Set output images
        id: build
        run: |
          echo "backend-image=${{ env.DOCKER_REGISTRY }}/backend:${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
          echo "frontend-image=${{ env.DOCKER_REGISTRY }}/frontend:${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT

  deploy-swarm:
    runs-on: ubuntu-latest
    needs: [detect-changes, build-images]
    if: needs.detect-changes.outputs.should-deploy == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Load AWS Configuration
        run: |
          echo "📋 Loading AWS configuration..."
          source config/load-aws-config.sh
          echo "EC2_HOST=$EC2_HOST" >> $GITHUB_ENV
          echo "EC2_PUBLIC_IP=$EC2_PUBLIC_IP" >> $GITHUB_ENV
          echo "EC2_USER=$EC2_USER" >> $GITHUB_ENV

      - name: Setup SSH Key
        run: |
          echo "🔑 Setting up SSH key..."
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/tuchanga.pem
          chmod 600 ~/.ssh/tuchanga.pem
          ssh-keyscan -H $EC2_HOST >> ~/.ssh/known_hosts

      - name: Deploy to Docker Swarm
        timeout-minutes: 10
        run: |
          echo "🚀 Deploying to Docker Swarm..."
          
          # Copy stack file and configs to EC2
          scp -i ~/.ssh/tuchanga.pem -o StrictHostKeyChecking=no \
            docker-stack.yml $EC2_USER@$EC2_HOST:/home/<USER>/tuchanga/
          
          scp -i ~/.ssh/tuchanga.pem -o StrictHostKeyChecking=no \
            config/nginx-proxy.conf $EC2_USER@$EC2_HOST:/home/<USER>/tuchanga/config/

          # Deploy to swarm
          ssh -i ~/.ssh/tuchanga.pem -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST "
            cd /home/<USER>/tuchanga
            
            # Update nginx config in swarm
            docker config rm changuapp_nginx_config 2>/dev/null || true
            docker config create changuapp_nginx_config config/nginx-proxy.conf
            
            # Deploy the stack
            export DOCKER_REGISTRY=${{ env.DOCKER_REGISTRY }}
            export IMAGE_TAG=${{ env.IMAGE_TAG }}
            docker stack deploy -c docker-stack.yml --with-registry-auth changuapp
            
            echo '✅ Stack deployed successfully!'
          "

      - name: Wait for services to be ready
        timeout-minutes: 8
        run: |
          echo "⏳ Waiting for services to be ready..."
          
          ssh -i ~/.ssh/tuchanga.pem -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST "
            # Wait for services to be running
            for i in {1..24}; do
              echo \"Checking services... attempt \$i/24\"
              
              # Check if all services are running
              RUNNING_SERVICES=\$(docker service ls --filter label=com.docker.stack.namespace=changuapp --format '{{.Replicas}}' | grep -c '1/1' || echo '0')
              TOTAL_SERVICES=\$(docker service ls --filter label=com.docker.stack.namespace=changuapp --format '{{.Name}}' | wc -l)
              
              echo \"Services running: \$RUNNING_SERVICES/\$TOTAL_SERVICES\"
              
              if [ \"\$RUNNING_SERVICES\" -eq \"\$TOTAL_SERVICES\" ] && [ \"\$TOTAL_SERVICES\" -gt 0 ]; then
                echo '✅ All services are running!'
                break
              fi
              
              if [ \$i -eq 24 ]; then
                echo '❌ Services failed to start within timeout'
                docker service ls
                exit 1
              fi
              
              sleep 10
            done
          "

      - name: Verify deployment
        timeout-minutes: 3
        run: |
          echo "🔍 Verifying deployment..."
          
          # Wait a bit for services to stabilize
          sleep 30
          
          # Test frontend
          FRONTEND_STATUS=$(timeout 30 curl -s -o /dev/null -w "%{http_code}" "http://$EC2_PUBLIC_IP/" || echo "000")
          echo "🌐 Frontend status: HTTP $FRONTEND_STATUS"
          
          # Test backend health
          BACKEND_STATUS=$(timeout 30 curl -s -o /dev/null -w "%{http_code}" "http://$EC2_PUBLIC_IP:8080/health" || echo "000")
          echo "🔧 Backend status: HTTP $BACKEND_STATUS"
          
          # Show service status
          ssh -i ~/.ssh/tuchanga.pem -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST "
            echo '📊 Docker Swarm services:'
            docker service ls --filter label=com.docker.stack.namespace=changuapp
            
            echo '📋 Service tasks:'
            docker stack ps changuapp --no-trunc
            
            echo '💾 Disk usage:'
            df -h /
          "
          
          # Final validation
          if [ "$FRONTEND_STATUS" = "200" ] && [ "$BACKEND_STATUS" = "200" ]; then
            echo "✅ Deployment verification successful!"
          else
            echo "❌ Deployment verification failed!"
            echo "   Frontend: $FRONTEND_STATUS (expected: 200)"
            echo "   Backend: $BACKEND_STATUS (expected: 200)"
            exit 1
          fi

  notify-completion:
    runs-on: ubuntu-latest
    needs: [detect-changes, deploy-swarm]
    if: always() && needs.detect-changes.outputs.should-deploy == 'true'
    steps:
      - name: Deployment Summary
        run: |
          if [[ "${{ needs.deploy-swarm.result }}" == "success" ]]; then
            echo "🎉 Docker Swarm deployment completed successfully!"
            echo "🌐 Frontend: http://${{ env.EC2_PUBLIC_IP }}/"
            echo "🔧 Backend API: http://${{ env.EC2_PUBLIC_IP }}:8080/api/"
            echo "❤️ Health Check: http://${{ env.EC2_PUBLIC_IP }}:8080/health"
          else
            echo "❌ Docker Swarm deployment failed! Check logs for details."
          fi
