# GitHub Secrets Setup Guide

To use the Docker Swarm deployment, you need to configure these GitHub secrets:

## Required Secrets

### 1. Docker Hub Authentication

**DOCKER_HUB_USERNAME**
- Your Docker Hub username
- Example: `myusername`

**DOCKER_HUB_TOKEN**
- Docker Hub access token (not your password!)
- How to create:
  1. Go to [Docker Hub](https://hub.docker.com/)
  2. Click your profile → Account Settings
  3. Go to Security tab
  4. Click "New Access Token"
  5. Name it "ChanguApp GitHub Actions"
  6. Copy the generated token

### 2. EC2 SSH Key (Already exists)

**EC2_SSH_KEY**
- Your existing EC2 private key
- This should already be configured

## How to Add Secrets

1. Go to your GitHub repository
2. Click **Settings** tab
3. In the left sidebar, click **Secrets and variables** → **Actions**
4. Click **New repository secret**
5. Add each secret:

```
Name: DOCKER_HUB_USERNAME
Value: your-docker-hub-username

Name: DOCKER_HUB_TOKEN  
Value: dckr_pat_xxxxxxxxxxxxxxxxxxxxxxxxxx
```

## Verification

After adding secrets, you should see:
- ✅ DOCKER_HUB_USERNAME
- ✅ DOCKER_HUB_TOKEN
- ✅ EC2_SSH_KEY (existing)

## Test Deployment

Once secrets are configured, test with:

```bash
git commit -m "Test Docker Swarm deployment --backend-deploy --frontend-deploy"
git push origin main
```

The new workflow will:
1. Build Docker images
2. Push to Docker Hub
3. Deploy to your EC2 Swarm cluster
4. Verify deployment health

## Troubleshooting

**If you get authentication errors:**
- Verify Docker Hub username is correct
- Regenerate Docker Hub token if needed
- Ensure token has read/write permissions

**If deployment fails:**
- Check GitHub Actions logs
- Verify EC2 instance is running
- Ensure Docker Swarm is initialized on EC2
