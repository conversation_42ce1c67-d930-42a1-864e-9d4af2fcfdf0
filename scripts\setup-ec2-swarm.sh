#!/bin/bash

# ChanguApp EC2 Docker Swarm Setup Script
# This script sets up Docker Swarm on your EC2 instance from your local machine
# Run this script from your project root directory

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 ChanguApp EC2 Docker Swarm Setup"
echo "===================================="

# Load AWS configuration
print_status "Loading AWS configuration..."
if [ ! -f "config/load-aws-config.sh" ]; then
    print_error "AWS configuration not found. Please run from project root."
    exit 1
fi

source config/load-aws-config.sh

print_success "Configuration loaded:"
echo "  Host: $EC2_HOST"
echo "  IP: $EC2_PUBLIC_IP"
echo "  User: $EC2_USER"
echo "  SSH Key: $SSH_KEY_PATH"
echo ""

# Check if SSH key exists
if [ ! -f "$SSH_KEY_PATH" ]; then
    print_error "SSH key not found at: $SSH_KEY_PATH"
    print_status "Please ensure your SSH key is in the correct location."
    exit 1
fi

# Test SSH connection
print_status "Testing SSH connection..."
if ! ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$EC2_USER@$EC2_HOST" "echo 'SSH connection successful'" 2>/dev/null; then
    print_error "Cannot connect to EC2 instance. Please check:"
    echo "  1. EC2 instance is running"
    echo "  2. Security group allows SSH (port 22)"
    echo "  3. SSH key is correct"
    exit 1
fi

print_success "SSH connection successful!"

# Copy required files to EC2
print_status "Copying setup files to EC2..."

# Create directories on EC2
ssh -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no "$EC2_USER@$EC2_HOST" "
    mkdir -p /home/<USER>/tuchanga/config
    mkdir -p /home/<USER>/tuchanga/scripts
"

# Copy migration script
scp -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no \
    scripts/migrate-to-swarm.sh "$EC2_USER@$EC2_HOST:/home/<USER>/"

# Copy nginx config
scp -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no \
    config/nginx-proxy.conf "$EC2_USER@$EC2_HOST:/home/<USER>/tuchanga/config/"

# Copy docker stack file
scp -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no \
    docker-stack.yml "$EC2_USER@$EC2_HOST:/home/<USER>/tuchanga/"

print_success "Files copied successfully!"

# Run migration script on EC2
print_status "Running Docker Swarm migration on EC2..."

ssh -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no "$EC2_USER@$EC2_HOST" "
    chmod +x /home/<USER>/migrate-to-swarm.sh
    /home/<USER>/migrate-to-swarm.sh
"

# Verify Swarm setup
print_status "Verifying Docker Swarm setup..."

SWARM_STATUS=$(ssh -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no "$EC2_USER@$EC2_HOST" "
    docker info --format '{{.Swarm.LocalNodeState}}'
" 2>/dev/null || echo "inactive")

if [ "$SWARM_STATUS" = "active" ]; then
    print_success "Docker Swarm is active!"
else
    print_error "Docker Swarm setup failed!"
    exit 1
fi

# Show Swarm status
print_status "Docker Swarm status:"
ssh -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no "$EC2_USER@$EC2_HOST" "
    echo '📊 Swarm Nodes:'
    docker node ls
    echo ''
    echo '🔐 Secrets:'
    docker secret ls
    echo ''
    echo '⚙️  Configs:'
    docker config ls
    echo ''
    echo '🌐 Networks:'
    docker network ls --filter driver=overlay
"

print_success "EC2 Docker Swarm setup completed! 🎉"
echo ""
print_status "Next steps:"
echo "1. Set up Docker Hub secrets in GitHub (see scripts/setup-github-secrets.md)"
echo "2. Test deployment with: git commit -m 'Test deployment --backend-deploy --frontend-deploy'"
echo "3. Monitor services with: docker service ls"
echo ""
print_status "Your EC2 instance is now ready for Docker Swarm deployments!"
echo "No more SSH timeout issues! 🚀"
