name: ChanguApp EC2 Simple Deployment

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

# Configuration is loaded dynamically from config/aws-config.env

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      deploy-frontend: ${{ steps.check.outputs.deploy-frontend }}
      deploy-backend: ${{ steps.check.outputs.deploy-backend }}
      should-deploy: ${{ steps.check.outputs.should-deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check deployment flags
        id: check
        run: |
          COMMIT_MESSAGE="${{ github.event.head_commit.message }}"
          SHOULD_DEPLOY=false

          if [[ "$COMMIT_MESSAGE" == *"--frontend-deploy"* ]]; then
            echo "deploy-frontend=true" >> $GITHUB_OUTPUT
            echo "🎨 Frontend deployment requested"
            SHOULD_DEPLOY=true
          else
            echo "deploy-frontend=false" >> $GITHUB_OUTPUT
          fi

          if [[ "$COMMIT_MESSAGE" == *"--backend-deploy"* ]]; then
            echo "deploy-backend=true" >> $GITHUB_OUTPUT
            echo "🔧 Backend deployment requested"
            SHOULD_DEPLOY=true
          else
            echo "deploy-backend=false" >> $GITHUB_OUTPUT
          fi

          echo "should-deploy=$SHOULD_DEPLOY" >> $GITHUB_OUTPUT

  deploy-to-ec2:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.should-deploy == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Load AWS Configuration
        run: |
          echo "📋 Loading AWS configuration..."
          source config/load-aws-config.sh
          echo "EC2_HOST=$EC2_HOST" >> $GITHUB_ENV
          echo "EC2_PUBLIC_IP=$EC2_PUBLIC_IP" >> $GITHUB_ENV
          echo "EC2_USER=$EC2_USER" >> $GITHUB_ENV
          echo "SSH_KEY_PATH=$SSH_KEY_PATH" >> $GITHUB_ENV
          echo "APP_DEPLOY_DIR=$APP_DEPLOY_DIR" >> $GITHUB_ENV
          echo "APP_WEB_DIR=$APP_WEB_DIR" >> $GITHUB_ENV
          echo "APP_ENV_FILE=$APP_ENV_FILE" >> $GITHUB_ENV
          echo "FRONTEND_URL=$FRONTEND_URL" >> $GITHUB_ENV
          echo "BACKEND_URL=$BACKEND_URL" >> $GITHUB_ENV
          echo "HEALTH_URL=$HEALTH_URL" >> $GITHUB_ENV

      - name: Setup SSH Key
        run: |
          echo "🔑 Setting up SSH key..."
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/tuchanga.pem
          chmod 600 ~/.ssh/tuchanga.pem
          ssh-keyscan -H $EC2_HOST >> ~/.ssh/known_hosts

          # Set SSH connection parameters with aggressive timeouts
          echo "SSH_OPTS=-i ~/.ssh/tuchanga.pem -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o ServerAliveInterval=30 -o ServerAliveCountMax=2 -o TCPKeepAlive=yes -o BatchMode=yes" >> $GITHUB_ENV

      - name: Clean Existing SSH Connections
        run: |
          echo "🧹 Cleaning any existing SSH connections..."
          # Kill any hung SSH connections to the instance
          pkill -f "ssh.*$EC2_HOST" || true
          echo "✅ Initial SSH cleanup completed"

      - name: Install System Dependencies
        timeout-minutes: 8
        run: |
          echo "📦 Installing system dependencies..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            set -e  # Exit on any error

            # Skip system update if Docker is already installed (faster deployment)
            if command -v docker &> /dev/null && docker --version &> /dev/null; then
              echo '🐳 Docker already installed, skipping system update'
            else
              echo '🔄 Installing essential packages only...'
              # Install only what we need, skip full system update
              sudo yum install -y docker nginx curl lsof || { echo 'Package installation failed'; exit 1; }
            fi

            # Ensure Docker is installed and running
            echo '🐳 Setting up Docker...'
            if ! command -v docker &> /dev/null; then
              sudo yum install -y docker || { echo 'Docker installation failed'; exit 1; }
            fi

            sudo systemctl start docker || echo 'Docker already running'
            sudo systemctl enable docker
            sudo usermod -a -G docker ec2-user

            # Verify Docker is running
            sudo systemctl is-active docker || { echo 'Docker failed to start'; exit 1; }

            # Install Docker Compose if not present
            if ! command -v docker-compose &> /dev/null && [ ! -f '/usr/local/bin/docker-compose' ]; then
              echo '🔧 Installing Docker Compose...'
              timeout 60 sudo curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose || { echo 'Docker Compose download failed'; exit 1; }
              sudo chmod +x /usr/local/bin/docker-compose
            else
              echo '🔧 Docker Compose already installed'
            fi

            # Verify Docker Compose installation
            /usr/local/bin/docker-compose --version || docker-compose --version || { echo 'Docker Compose installation failed'; exit 1; }

            # Ensure nginx is installed
            if ! command -v nginx &> /dev/null; then
              echo '🌐 Installing Nginx...'
              sudo yum install -y nginx || { echo 'Nginx installation failed'; exit 1; }
            else
              echo '🌐 Nginx already installed'
            fi

            # Remove any existing PostgreSQL installations to avoid port conflicts
            echo '🗑️ Cleaning PostgreSQL conflicts...'
            sudo systemctl stop postgresql 2>/dev/null || echo 'PostgreSQL not running'
            sudo systemctl disable postgresql 2>/dev/null || echo 'PostgreSQL not enabled'

            # Kill any processes using PostgreSQL ports
            sudo lsof -ti:5432 | xargs -r sudo kill -9 2>/dev/null || echo 'No processes on port 5432'

            echo '✅ Dependencies ready'
          "



      - name: Copy Repository Files
        timeout-minutes: 5
        run: |
          echo "📤 Copying repository files to EC2..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "mkdir -p /home/<USER>/tuchanga"

          # Use rsync with aggressive timeout and progress monitoring
          timeout 300 rsync -avz --delete --timeout=60 --progress \
            -e "ssh $SSH_OPTS" \
            --exclude='.git' \
            --exclude='node_modules' \
            --exclude='dist' \
            --exclude='build' \
            --exclude='.env' \
            --exclude='.env.local' \
            --exclude='.env.development' \
            --exclude='*.log' \
            --exclude='.DS_Store' \
            ./ $EC2_USER@$EC2_HOST:/home/<USER>/tuchanga/ || { echo 'File copy failed or timed out'; exit 1; }

      - name: Deploy Backend
        if: needs.detect-changes.outputs.deploy-backend == 'true'
        timeout-minutes: 15
        run: |
          echo "🔧 Deploying backend with Docker Compose from root..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            set -e  # Exit on any error
            cd /home/<USER>/tuchanga

            # Stop and disable native PostgreSQL service to free port 5432
            echo '🛑 Stopping native PostgreSQL service...'
            sudo systemctl stop postgresql || echo 'PostgreSQL service not running'
            sudo systemctl disable postgresql || echo 'PostgreSQL service not enabled'

            # Kill any processes using port 5432
            echo '🔍 Checking for processes on port 5432...'
            sudo lsof -ti:5432 | xargs -r sudo kill -9 || echo 'No processes on port 5432'

            # Stop any existing containers with timeout
            echo '🛑 Stopping existing containers...'
            timeout 120 docker-compose down --timeout 30 || echo 'Timeout stopping containers'

            # Remove old images and containers to free space
            echo '🧹 Cleaning up Docker resources...'
            docker system prune -f --volumes || echo 'No resources to prune'
            docker container prune -f || echo 'No containers to prune'

            # Check available disk space
            echo '💾 Checking disk space...'
            df -h /

            # Load production environment variables
            echo '🔧 Loading production environment...'
            export \$(cat .env.production | grep -v '^#' | xargs) || echo 'No .env.production file found'

            # Build and start services with timeout
            echo '🔨 Building and starting services...'
            timeout 600 docker-compose up -d --build --force-recreate

            # Wait for PostgreSQL to be ready
            echo '⏳ Waiting for PostgreSQL to be ready...'
            for i in {1..30}; do
              if docker-compose exec -T postgres pg_isready -U postgres -d job_platform; then
                echo '✅ PostgreSQL is ready'
                break
              fi
              echo \"Attempt \$i/30: PostgreSQL not ready yet...\"
              sleep 5
            done

            # Wait for backend to be ready
            echo '⏳ Waiting for backend to be ready...'
            for i in {1..20}; do
              if curl -f http://localhost:3000/health >/dev/null 2>&1; then
                echo '✅ Backend is ready'
                break
              fi
              echo \"Attempt \$i/20: Backend not ready yet...\"
              sleep 10
            done

            # Check if services are running
            echo '📊 Checking service status...'
            docker-compose ps

            # Show recent logs for debugging
            echo '📋 Recent backend logs:'
            docker-compose logs backend --tail=10

            echo '📋 Recent postgres logs:'
            docker-compose logs postgres --tail=5

            # Final health check
            echo '🔍 Final health check...'
            if curl -f http://localhost:3000/health; then
              echo '✅ Backend deployed successfully with Docker Compose'
            else
              echo '❌ Backend health check failed'
              exit 1
            fi
          "

      - name: Setup Nginx Configuration
        run: |
          echo "🌐 Setting up Nginx configuration..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            # Create nginx configuration for ChanguApp
            sudo tee /etc/nginx/conf.d/changuapp.conf > /dev/null << 'EOF'
            server {
                listen 80;
                server_name _;
                root /var/www/tuchanga/frontend;
                index index.html;

                # Security headers
                add_header X-Frame-Options \"SAMEORIGIN\" always;
                add_header X-XSS-Protection \"1; mode=block\" always;
                add_header X-Content-Type-Options \"nosniff\" always;

                # Frontend
                location / {
                    try_files \$uri \$uri/ /index.html;

                    # Cache static assets
                    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
                        expires 1y;
                        add_header Cache-Control \"public, immutable\";
                    }

                    # No cache for HTML files
                    location ~* \.html\$ {
                        add_header Cache-Control \"no-cache, no-store, must-revalidate\";
                        add_header Pragma \"no-cache\";
                        add_header Expires \"0\";
                    }
                }

                # Backend API proxy
                location /api/ {
                    proxy_pass http://localhost:3000/;
                    proxy_http_version 1.1;
                    proxy_set_header Upgrade \$http_upgrade;
                    proxy_set_header Connection 'upgrade';
                    proxy_set_header Host \$host;
                    proxy_set_header X-Real-IP \$remote_addr;
                    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto \$scheme;
                    proxy_cache_bypass \$http_upgrade;

                    # Timeout settings
                    proxy_connect_timeout 60s;
                    proxy_send_timeout 60s;
                    proxy_read_timeout 60s;
                }

                # Health check endpoint
                location /health {
                    proxy_pass http://localhost:3000/health;
                    access_log off;
                }

                # Direct backend access (for API calls without /api prefix)
                location ~ ^/(auth|jobs|users|chat|notifications|applications)/ {
                    proxy_pass http://localhost:3000;
                    proxy_http_version 1.1;
                    proxy_set_header Upgrade \$http_upgrade;
                    proxy_set_header Connection 'upgrade';
                    proxy_set_header Host \$host;
                    proxy_set_header X-Real-IP \$remote_addr;
                    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto \$scheme;
                    proxy_cache_bypass \$http_upgrade;

                    # Timeout settings
                    proxy_connect_timeout 60s;
                    proxy_send_timeout 60s;
                    proxy_read_timeout 60s;
                }
            }
          EOF

            # Remove default nginx configuration
            sudo rm -f /etc/nginx/sites-enabled/default
            sudo rm -f /etc/nginx/conf.d/default.conf

            # Test nginx configuration
            sudo nginx -t

            echo 'Nginx configuration setup completed'
          "

      - name: Deploy Frontend
        if: needs.detect-changes.outputs.deploy-frontend == 'true'
        timeout-minutes: 12
        run: |
          echo "🎨 Deploying frontend..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            set -e  # Exit on any error
            cd /home/<USER>/tuchanga/frontend

            # Check Node.js version
            echo '📋 Node.js version:'
            node --version

            # Install dependencies with timeout and memory optimization
            echo '📦 Installing dependencies...'
            npm ci || { echo 'npm ci timeout or failed'; exit 1; }

            # Set production environment for build
            export NODE_ENV=production

            # Build with timeout and memory optimization
            echo '🔨 Building frontend...'
            timeout 600 npm run build || { echo 'npm build timeout or failed'; exit 1; }

            # Verify build output exists
            if [ ! -d \"dist\" ]; then
              echo '❌ Build failed - dist directory not found'
              exit 1
            fi

            # Deploy to nginx
            echo '🚀 Deploying to nginx...'
            sudo mkdir -p /var/www/tuchanga/frontend
            sudo rm -rf /var/www/tuchanga/frontend/*
            sudo cp -r dist/* /var/www/tuchanga/frontend/

            # Set proper permissions
            sudo chown -R nginx:nginx /var/www/tuchanga/frontend
            sudo chmod -R 755 /var/www/tuchanga/frontend

            # Test nginx configuration
            echo '🔍 Testing nginx configuration...'
            sudo nginx -t || { echo 'Nginx configuration test failed'; exit 1; }

            # Restart nginx
            echo '🔄 Restarting nginx...'
            sudo systemctl restart nginx

            # Verify nginx is running
            sudo systemctl is-active nginx || { echo 'Nginx failed to start'; exit 1; }

            echo '✅ Frontend deployed successfully'
          "

      - name: Verify Deployment
        timeout-minutes: 5
        run: |
          echo "🔍 Verifying deployment..."
          sleep 15  # Give services more time to stabilize

          # Test frontend with timeout
          echo "🌐 Testing frontend..."
          FRONTEND_STATUS=$(timeout 30 curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" || echo "000")
          echo "🌐 Frontend status: HTTP $FRONTEND_STATUS"

          # Test backend with timeout
          echo "🔧 Testing backend..."
          BACKEND_STATUS=$(timeout 30 curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" || echo "000")
          echo "🔧 Backend status: HTTP $BACKEND_STATUS"

          # Check services on EC2
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            echo '📊 Docker services status:'
            cd /home/<USER>/tuchanga
            docker-compose ps || echo 'Docker Compose not running'

            echo '🌐 Nginx status:'
            sudo systemctl is-active nginx || echo 'Nginx not running'

            echo '💾 Disk usage:'
            df -h /

            echo '🔧 Backend container logs (last 10 lines):'
            docker-compose logs backend --tail=10 || echo 'No backend logs'

            echo '🗄️ Database container logs (last 5 lines):'
            docker-compose logs postgres --tail=5 || echo 'No database logs'

            echo '🔍 Local backend health check:'
            timeout 10 curl -f http://localhost:3000/health || echo 'Local health check failed'

            echo '🔍 Port 3000 status:'
            sudo netstat -tlnp | grep :3000 || echo 'Port 3000 not listening'

            echo '🔍 Port 5432 status:'
            sudo netstat -tlnp | grep :5432 || echo 'Port 5432 not listening'
          "

          # Final validation
          if [ "$FRONTEND_STATUS" = "200" ] && [ "$BACKEND_STATUS" = "200" ]; then
            echo "✅ Deployment verification successful!"
          else
            echo "❌ Deployment verification failed!"
            echo "   Frontend: $FRONTEND_STATUS (expected: 200)"
            echo "   Backend: $BACKEND_STATUS (expected: 200)"
            exit 1
          fi

      - name: Cleanup SSH Connections
        if: always()
        run: |
          echo "🧹 Cleaning up SSH connections..."
          # Kill any remaining SSH processes
          pkill -f "ssh.*$EC2_HOST" || true
          echo "✅ SSH cleanup completed"

  notify-completion:
    runs-on: ubuntu-latest
    needs: [detect-changes, deploy-to-ec2]
    if: always() && needs.detect-changes.outputs.should-deploy == 'true'
    steps:
      - name: Deployment Summary
        run: |
          if [[ "${{ needs.deploy-to-ec2.result }}" == "success" ]]; then
            echo "🎉 Deployment completed successfully!"
            echo " Frontend: http://${{ env.EC2_HOST }}/"
            echo "🔧 Backend API: http://${{ env.EC2_HOST }}:3000/"
          else
            echo "❌ Deployment failed! Check logs for details."
          fi
