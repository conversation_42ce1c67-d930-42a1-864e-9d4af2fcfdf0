# ChanguApp - Plataforma de Trabajos Casuales

Una plataforma fullstack para trabajos casuales y de corto plazo como jardinería, paseo de perros, reparaciones y trabajos de mantenimiento general.

## 🏗️ Arquitectura

- **Frontend**: React + TypeScript + Vite + Chakra UI
- **Backend**: NestJS + TypeScript + PostgreSQL
- **Autenticación**: Firebase Auth
- **Deployment**: Docker + AWS EC2
- **CI/CD**: GitHub Actions

## 🚀 Inicio Rápido con Docker

### Prerrequisitos
- Docker y Docker Compose instalados
- Node.js 22+ (para desarrollo local)

### 1. Clonar el Repositorio
```bash
git clone <repository-url>
cd tuchanga
```

### 2. Configurar Variables de Entorno

Crea un archivo `.env` en la raíz del proyecto:
```env
# Database
POSTGRES_DB=job_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123

# Backend
NODE_ENV=development
PORT=3000
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres123
DB_DATABASE=job_platform
JWT_SECRET=dev-jwt-secret-key-change-in-production
JWT_EXPIRES_IN=24h
FIREBASE_PROJECT_ID=tu-changa-583b3
CORS_ORIGIN=http://localhost:5173
```

### 3. Ejecutar con Docker Compose

#### Desarrollo
```bash
# Iniciar solo la base de datos
docker-compose up postgres -d

# Luego ejecutar frontend y backend localmente
cd frontend && npm install && npm run dev
cd backend && npm install && npm run start:dev
```

#### Producción
```bash
# Cargar variables de entorno de producción y iniciar servicios
export $(cat .env.production | grep -v '^#' | xargs)
docker-compose up -d --build
```

## 📁 Estructura del Proyecto

```
tuchanga/
├── docker-compose.yml          # Configuración de Docker (dev y prod)
├── .env.production            # Variables de entorno de producción
├── frontend/                   # Aplicación React
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── backend/                    # API NestJS
│   ├── src/
│   ├── Dockerfile
│   ├── init-scripts/          # Scripts de inicialización de DB
│   └── package.json
├── config/                     # Configuración de AWS
├── scripts/                    # Scripts de utilidad
└── .github/workflows/         # CI/CD con GitHub Actions
```

## 🔧 Desarrollo Local

### Frontend
```bash
cd frontend
npm install
npm run dev
```
Accede a: http://localhost:5173

### Backend
```bash
cd backend
npm install
npm run start:dev
```
API disponible en: http://localhost:3000

### Base de Datos
```bash
# Iniciar PostgreSQL con Docker
docker-compose up postgres -d
```

## 🚀 Deployment

### Automático (GitHub Actions)
```bash
# Deploy del backend
git commit -m "Update backend --backend-deploy"
git push

# Deploy del frontend
git commit -m "Update frontend --frontend-deploy"
git push

# Deploy completo
git commit -m "Full deployment --backend-deploy --frontend-deploy"
git push
```

### Manual
```bash
# SSH al servidor
ssh -i backend/keys/tuchanga.pem ec2-user@<EC2_HOST>

# Navegar al directorio
cd /home/<USER>/tuchanga

# Actualizar código
git pull

# Cargar variables de producción y rebuild
export $(cat .env.production | grep -v '^#' | xargs)
docker-compose up -d --build
```

## 🔍 Monitoreo y Debug

### Health Checks
- Backend: http://localhost:3000/health
- Frontend: http://localhost:5173

### Logs de Docker
```bash
# Ver logs del backend
docker-compose logs backend -f

# Ver logs de la base de datos
docker-compose logs postgres -f

# Ver todos los logs
docker-compose logs -f
```

### Script de Debug
```bash
# En el servidor EC2
./scripts/debug-deployment.sh
```

## 🛠️ Comandos Útiles

### Docker
```bash
# Ver servicios corriendo
docker-compose ps

# Parar todos los servicios
docker-compose down

# Rebuild completo
docker-compose up -d --build --force-recreate

# Limpiar recursos
docker system prune -f
```

### Base de Datos
```bash
# Conectar a PostgreSQL
docker-compose exec postgres psql -U postgres -d job_platform

# Backup
docker-compose exec postgres pg_dump -U postgres job_platform > backup.sql

# Restore
docker-compose exec -T postgres psql -U postgres job_platform < backup.sql
```

## 🔐 Configuración de Producción

### Variables de Entorno de Producción
- Cambiar `JWT_SECRET` por una clave segura
- Configurar `FIREBASE_PROJECT_ID` con tu proyecto
- Ajustar `CORS_ORIGIN` para tu dominio
- Usar contraseñas seguras para la base de datos

### SSL/HTTPS
Para producción, configura un certificado SSL usando Let's Encrypt o CloudFlare.

## 📚 Documentación Adicional

- [Backend README](./backend/README.md)
- [Frontend README](./frontend/README.md)
- [Deployment Guide](./backend/DEPLOYMENT.md)
- [AWS Configuration](./config/README.md)

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.
