#!/usr/bin/env node

/**
 * Simple production server starter for ChanguApp Backend
 * This script provides a simple way to start the backend in production
 * without requiring PM2 or other process managers.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const CONFIG = {
  script: path.join(__dirname, 'dist', 'src', 'main.js'),
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'production',
  maxRestarts: 10,
  restartDelay: 5000, // 5 seconds
};

let restartCount = 0;
let child = null;

// Ensure the built application exists
if (!fs.existsSync(CONFIG.script)) {
  console.error('❌ Built application not found. Please run "npm run build" first.');
  console.error(`   Looking for: ${CONFIG.script}`);
  process.exit(1);
}

function startServer() {
  console.log(`🚀 Starting ChanguApp Backend (attempt ${restartCount + 1})`);
  console.log(`📁 Working directory: ${__dirname}`);
  console.log(`🎯 Script: ${CONFIG.script}`);
  console.log(`🌐 Port: ${CONFIG.port}`);
  console.log(`🔧 Environment: ${CONFIG.nodeEnv}`);
  console.log('─'.repeat(50));

  child = spawn('node', [CONFIG.script], {
    cwd: __dirname,
    env: {
      ...process.env,
      NODE_ENV: CONFIG.nodeEnv,
      PORT: CONFIG.port,
    },
    stdio: 'inherit'
  });

  child.on('exit', (code, signal) => {
    if (signal) {
      console.log(`\n🛑 Server terminated by signal: ${signal}`);
      process.exit(0);
    } else if (code === 0) {
      console.log('\n✅ Server exited normally');
      process.exit(0);
    } else {
      console.log(`\n💥 Server crashed with code: ${code}`);
      
      if (restartCount < CONFIG.maxRestarts) {
        restartCount++;
        console.log(`🔄 Restarting in ${CONFIG.restartDelay / 1000} seconds... (${restartCount}/${CONFIG.maxRestarts})`);
        setTimeout(startServer, CONFIG.restartDelay);
      } else {
        console.log(`❌ Max restart attempts (${CONFIG.maxRestarts}) reached. Giving up.`);
        process.exit(1);
      }
    }
  });

  child.on('error', (err) => {
    console.error('❌ Failed to start server:', err.message);
    process.exit(1);
  });
}

// Handle graceful shutdown
function gracefulShutdown(signal) {
  console.log(`\n🛑 Received ${signal}. Shutting down gracefully...`);
  if (child) {
    child.kill('SIGTERM');
    setTimeout(() => {
      if (child && !child.killed) {
        console.log('⚠️  Force killing server...');
        child.kill('SIGKILL');
      }
    }, 10000); // 10 seconds timeout
  } else {
    process.exit(0);
  }
}

// Register signal handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start the server
console.log('🎉 ChanguApp Backend Production Starter');
console.log('═'.repeat(50));
startServer();
