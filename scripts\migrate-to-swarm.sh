#!/bin/bash

# ChanguApp Migration Script: Docker Compose → Docker Swarm
# This script helps migrate from SSH-based docker-compose to Docker Swarm
# Run this script ON YOUR EC2 INSTANCE

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔄 ChanguApp Migration: Docker Compose → Docker Swarm"
echo "===================================================="
echo ""

# Check if running on EC2
if ! curl -s --max-time 5 http://***************/latest/meta-data/instance-id &>/dev/null; then
    print_error "This script should be run on your EC2 instance, not locally!"
    exit 1
fi

print_status "Running migration on EC2 instance..."

# Step 1: Backup current deployment
print_status "Step 1: Backing up current deployment..."

BACKUP_DIR="/home/<USER>/backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup docker-compose data
if [ -d "/home/<USER>/tuchanga" ]; then
    print_status "Backing up application data..."
    cp -r /home/<USER>/tuchanga "$BACKUP_DIR/"
fi

# Backup nginx config
if [ -f "/etc/nginx/conf.d/changuapp.conf" ]; then
    print_status "Backing up nginx configuration..."
    sudo cp /etc/nginx/conf.d/changuapp.conf "$BACKUP_DIR/"
fi

print_success "Backup created at: $BACKUP_DIR"

# Step 2: Stop current services
print_status "Step 2: Stopping current services..."

# Stop docker-compose services
if [ -f "/home/<USER>/tuchanga/docker-compose.yml" ]; then
    cd /home/<USER>/tuchanga
    print_status "Stopping docker-compose services..."
    docker-compose down --timeout 30 || print_warning "Some containers may not have stopped gracefully"
fi

# Stop nginx
print_status "Stopping nginx..."
sudo systemctl stop nginx || print_warning "Nginx was not running"

print_success "Current services stopped"

# Step 3: Clean up old resources
print_status "Step 3: Cleaning up old resources..."

# Remove old containers and images
print_status "Cleaning up Docker resources..."
docker container prune -f || true
docker image prune -f || true
docker volume prune -f || true

print_success "Cleanup completed"

# Step 4: Initialize Docker Swarm
print_status "Step 4: Initializing Docker Swarm..."

# Check if already in swarm mode
if docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
    print_warning "Docker Swarm is already initialized"
else
    # Get private IP
    PRIVATE_IP=$(curl -s http://***************/latest/meta-data/local-ipv4)
    print_status "Initializing Docker Swarm with IP: $PRIVATE_IP"
    
    docker swarm init --advertise-addr $PRIVATE_IP
    print_success "Docker Swarm initialized!"
fi

# Step 5: Create Swarm secrets
print_status "Step 5: Creating Docker Swarm secrets..."

# Generate secure passwords
POSTGRES_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)

# Create secrets
echo "$POSTGRES_PASSWORD" | docker secret create changuapp_postgres_password - 2>/dev/null || print_warning "Postgres password secret already exists"
echo "$JWT_SECRET" | docker secret create changuapp_jwt_secret - 2>/dev/null || print_warning "JWT secret already exists"

print_success "Secrets created"

# Step 6: Create overlay network
print_status "Step 6: Creating overlay network..."

docker network create --driver overlay --attachable changuapp-network 2>/dev/null || print_warning "Network already exists"

print_success "Network created"

# Step 7: Create nginx config
print_status "Step 7: Setting up nginx configuration..."

if [ -f "/home/<USER>/tuchanga/config/nginx-proxy.conf" ]; then
    docker config create changuapp_nginx_config /home/<USER>/tuchanga/config/nginx-proxy.conf 2>/dev/null || print_warning "Nginx config already exists"
    print_success "Nginx configuration created"
else
    print_error "Nginx configuration file not found at /home/<USER>/tuchanga/config/nginx-proxy.conf"
    print_status "Please ensure the file exists before deploying the stack"
fi

# Step 8: Show migration status
print_status "Step 8: Migration status..."

echo ""
print_success "Migration completed successfully! 🎉"
echo ""
print_status "Docker Swarm status:"
docker node ls
echo ""
print_status "Available secrets:"
docker secret ls
echo ""
print_status "Available configs:"
docker config ls
echo ""
print_status "Available networks:"
docker network ls --filter driver=overlay

echo ""
print_status "Next steps:"
echo "1. Deploy your application with: docker stack deploy -c docker-stack.yml changuapp"
echo "2. Check service status with: docker service ls"
echo "3. View logs with: docker service logs changuapp_backend"
echo ""
print_status "Your backup is available at: $BACKUP_DIR"
echo ""
print_warning "Important notes:"
echo "• Your old docker-compose setup has been stopped"
echo "• Use 'docker stack' commands instead of 'docker-compose'"
echo "• Services are now managed by Docker Swarm"
echo "• GitHub Actions should use the new swarm-deploy.yml workflow"
echo ""
print_success "Migration to Docker Swarm completed! 🚀"
