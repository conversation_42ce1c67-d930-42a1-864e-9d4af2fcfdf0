#!/bin/bash

# ChanguApp Docker Image Build and Push Script
# This script builds Docker images for frontend and backend
# Usage: ./build-images.sh [frontend|backend|all] [tag] [registry]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BUILD_TARGET="${1:-all}"
IMAGE_TAG="${2:-latest}"
DOCKER_REGISTRY="${3:-changuapp}"
PUSH_IMAGES="${PUSH_IMAGES:-true}"

echo "🔨 ChanguApp Docker Image Builder"
echo "================================="
echo "Target: $BUILD_TARGET"
echo "Tag: $IMAGE_TAG"
echo "Registry: $DOCKER_REGISTRY"
echo "Push: $PUSH_IMAGES"
echo ""

# Function to build and optionally push an image
build_image() {
    local service=$1
    local context_dir=$2
    local dockerfile=$3
    local image_name="${DOCKER_REGISTRY}/${service}:${IMAGE_TAG}"
    
    print_status "Building $service image..."
    echo "Context: $context_dir"
    echo "Dockerfile: $dockerfile"
    echo "Image: $image_name"
    echo ""
    
    # Build the image
    if docker build -t "$image_name" -f "$dockerfile" "$context_dir"; then
        print_success "Successfully built $image_name"
        
        # Tag as latest if not already latest
        if [[ "$IMAGE_TAG" != "latest" ]]; then
            docker tag "$image_name" "${DOCKER_REGISTRY}/${service}:latest"
            print_status "Tagged as ${DOCKER_REGISTRY}/${service}:latest"
        fi
        
        # Push to registry if enabled
        if [[ "$PUSH_IMAGES" == "true" ]]; then
            print_status "Pushing $image_name to registry..."
            if docker push "$image_name"; then
                print_success "Successfully pushed $image_name"
                
                # Push latest tag if not already latest
                if [[ "$IMAGE_TAG" != "latest" ]]; then
                    docker push "${DOCKER_REGISTRY}/${service}:latest"
                    print_success "Successfully pushed ${DOCKER_REGISTRY}/${service}:latest"
                fi
            else
                print_error "Failed to push $image_name"
                return 1
            fi
        fi
        
        # Show image info
        print_status "Image details:"
        docker images "$image_name" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        echo ""
        
    else
        print_error "Failed to build $image_name"
        return 1
    fi
}

# Function to check if Docker is logged in (for pushing)
check_docker_login() {
    if [[ "$PUSH_IMAGES" == "true" ]]; then
        print_status "Checking Docker registry authentication..."
        
        # Try to get auth info (this will fail if not logged in to Docker Hub)
        if ! docker info | grep -q "Username"; then
            print_warning "Not logged in to Docker registry"
            print_status "To push images, login with: docker login"
            print_status "Or set PUSH_IMAGES=false to skip pushing"
            
            read -p "Continue without pushing? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
            PUSH_IMAGES="false"
        else
            print_success "Docker registry authentication OK"
        fi
    fi
}

# Function to cleanup old images
cleanup_old_images() {
    print_status "Cleaning up old images..."
    
    # Remove dangling images
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true
        print_success "Removed dangling images"
    fi
    
    # Remove old versions (keep last 3)
    for service in frontend backend; do
        local old_images=$(docker images "${DOCKER_REGISTRY}/${service}" --format "{{.ID}}" | tail -n +4)
        if [[ -n "$old_images" ]]; then
            echo "$old_images" | xargs docker rmi 2>/dev/null || true
            print_success "Cleaned up old $service images"
        fi
    done
}

# Pre-build checks
print_status "Running pre-build checks..."

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Check Docker login if pushing
check_docker_login

# Check if required directories exist
if [[ "$BUILD_TARGET" == "frontend" || "$BUILD_TARGET" == "all" ]]; then
    if [[ ! -d "frontend" ]]; then
        print_error "Frontend directory not found"
        exit 1
    fi
    if [[ ! -f "frontend/Dockerfile" ]]; then
        print_error "Frontend Dockerfile not found"
        exit 1
    fi
fi

if [[ "$BUILD_TARGET" == "backend" || "$BUILD_TARGET" == "all" ]]; then
    if [[ ! -d "backend" ]]; then
        print_error "Backend directory not found"
        exit 1
    fi
    if [[ ! -f "backend/Dockerfile" ]]; then
        print_error "Backend Dockerfile not found"
        exit 1
    fi
fi

print_success "Pre-build checks passed!"

# Build images based on target
case $BUILD_TARGET in
    "frontend")
        build_image "frontend" "./frontend" "./frontend/Dockerfile"
        ;;
    "backend")
        build_image "backend" "./backend" "./backend/Dockerfile"
        ;;
    "all")
        build_image "backend" "./backend" "./backend/Dockerfile"
        build_image "frontend" "./frontend" "./frontend/Dockerfile"
        ;;
    *)
        print_error "Invalid build target: $BUILD_TARGET"
        print_status "Valid targets: frontend, backend, all"
        exit 1
        ;;
esac

# Cleanup old images
cleanup_old_images

# Summary
print_success "Build completed! 🎉"
echo ""
print_status "Built images:"
docker images "${DOCKER_REGISTRY}/*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
echo ""

if [[ "$PUSH_IMAGES" == "true" ]]; then
    print_status "Images have been pushed to registry: $DOCKER_REGISTRY"
else
    print_status "Images built locally. To push to registry:"
    echo "docker push ${DOCKER_REGISTRY}/backend:${IMAGE_TAG}"
    echo "docker push ${DOCKER_REGISTRY}/frontend:${IMAGE_TAG}"
fi

echo ""
print_status "Next steps:"
echo "1. Deploy to swarm: ./scripts/swarm-deploy.sh $BUILD_TARGET $IMAGE_TAG"
echo "2. Check deployment: docker service ls"
echo "3. View logs: docker service logs changuapp_backend"
