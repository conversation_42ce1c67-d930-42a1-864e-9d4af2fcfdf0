# Docker Swarm Stack for ChanguApp Production Deployment
# This file replaces docker-compose.yml for production deployments
# Deploy with: docker stack deploy -c docker-stack.yml changuapp

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: job_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - changuapp-network
    secrets:
      - postgres_password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d job_platform"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # NestJS Backend API
  backend:
    image: ${DOCKER_REGISTRY:-changuapp}/backend:${IMAGE_TAG:-latest}
    environment:
      NODE_ENV: production
      HOST: 0.0.0.0
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD_FILE: /run/secrets/postgres_password
      DB_DATABASE: job_platform
      JWT_SECRET_FILE: /run/secrets/jwt_secret
      JWT_EXPIRES_IN: 24h
      FIREBASE_PROJECT_ID: tu-changa-583b3
      CORS_ORIGIN: "*"
      API_PREFIX: ""
    networks:
      - changuapp-network
    secrets:
      - postgres_password
      - jwt_secret
    healthcheck:
      test: ["CMD", "node", "/app/health-check.js"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 15s
        max_attempts: 5
        window: 180s
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
      rollback_config:
        parallelism: 1
        delay: 10s
        failure_action: pause
        monitor: 60s
        max_failure_ratio: 0.3

  # Frontend (Nginx)
  frontend:
    image: ${DOCKER_REGISTRY:-changuapp}/frontend:${IMAGE_TAG:-latest}
    ports:
      - "80:80"
    networks:
      - changuapp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 30s
        max_failure_ratio: 0.3

  # Reverse Proxy (Nginx for API routing)
  proxy:
    image: nginx:alpine
    ports:
      - "8080:80"
    networks:
      - changuapp-network
    configs:
      - source: nginx_config
        target: /etc/nginx/conf.d/default.conf
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          memory: 64M
        reservations:
          memory: 32M

# Docker Swarm Secrets (secure environment variables)
secrets:
  postgres_password:
    external: true
    name: changuapp_postgres_password
  jwt_secret:
    external: true
    name: changuapp_jwt_secret

# Docker Swarm Configs
configs:
  nginx_config:
    external: true
    name: changuapp_nginx_config

# Persistent Volumes
volumes:
  postgres_data:
    driver: local

# Overlay Network for service communication
networks:
  changuapp-network:
    driver: overlay
    attachable: true
    ipam:
      config:
        - subnet: ********/24
