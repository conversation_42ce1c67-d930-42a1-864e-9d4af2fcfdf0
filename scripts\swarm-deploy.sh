#!/bin/bash

# ChanguApp Docker Swarm Deployment Script
# This script deploys or updates the ChanguApp stack in Docker Swarm
# Usage: ./swarm-deploy.sh [frontend|backend|all] [image_tag]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
STACK_NAME="changuapp"
STACK_FILE="docker-stack.yml"
DOCKER_REGISTRY="${DOCKER_REGISTRY:-changuapp}"
IMAGE_TAG="${2:-latest}"
DEPLOY_TARGET="${1:-all}"

echo "🚀 ChanguApp Docker Swarm Deployment"
echo "===================================="
echo "Stack: $STACK_NAME"
echo "Target: $DEPLOY_TARGET"
echo "Image Tag: $IMAGE_TAG"
echo "Registry: $DOCKER_REGISTRY"
echo ""

# Check if Docker Swarm is initialized
if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
    print_error "Docker Swarm is not initialized. Run ./scripts/swarm-init.sh first."
    exit 1
fi

# Check if stack file exists
if [[ ! -f "$STACK_FILE" ]]; then
    print_error "Stack file $STACK_FILE not found!"
    exit 1
fi

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local max_wait=${2:-300}  # 5 minutes default
    local wait_time=0
    
    print_status "Waiting for service $service_name to be ready..."
    
    while [[ $wait_time -lt $max_wait ]]; do
        local replicas=$(docker service ls --filter name=$service_name --format "{{.Replicas}}" | head -1)
        
        if [[ "$replicas" == "1/1" ]]; then
            print_success "Service $service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 5
        wait_time=$((wait_time + 5))
    done
    
    print_error "Service $service_name failed to become ready within ${max_wait}s"
    return 1
}

# Function to check service health
check_service_health() {
    local service_name=$1
    print_status "Checking health of $service_name..."
    
    # Get service tasks
    local tasks=$(docker service ps $service_name --format "{{.CurrentState}}" --no-trunc)
    echo "$tasks"
    
    # Check if any task is in failed state
    if echo "$tasks" | grep -q "Failed"; then
        print_warning "Some tasks for $service_name have failed"
        docker service logs $service_name --tail 20
        return 1
    fi
    
    return 0
}

# Function to deploy specific services
deploy_service() {
    local service_type=$1
    
    case $service_type in
        "frontend")
            print_status "Deploying frontend service..."
            export DOCKER_REGISTRY IMAGE_TAG
            docker stack deploy -c $STACK_FILE --with-registry-auth $STACK_NAME
            wait_for_service "${STACK_NAME}_frontend"
            ;;
        "backend")
            print_status "Deploying backend service..."
            export DOCKER_REGISTRY IMAGE_TAG
            docker stack deploy -c $STACK_FILE --with-registry-auth $STACK_NAME
            wait_for_service "${STACK_NAME}_postgres" 120
            wait_for_service "${STACK_NAME}_backend" 180
            ;;
        "all")
            print_status "Deploying full stack..."
            export DOCKER_REGISTRY IMAGE_TAG
            docker stack deploy -c $STACK_FILE --with-registry-auth $STACK_NAME
            
            # Wait for services in dependency order
            wait_for_service "${STACK_NAME}_postgres" 120
            wait_for_service "${STACK_NAME}_backend" 180
            wait_for_service "${STACK_NAME}_frontend" 120
            wait_for_service "${STACK_NAME}_proxy" 60
            ;;
        *)
            print_error "Invalid deployment target: $service_type"
            print_status "Valid targets: frontend, backend, all"
            exit 1
            ;;
    esac
}

# Pre-deployment checks
print_status "Running pre-deployment checks..."

# Check if required secrets exist
if ! docker secret ls | grep -q "changuapp_postgres_password"; then
    print_error "Required secret 'changuapp_postgres_password' not found. Run ./scripts/swarm-init.sh first."
    exit 1
fi

if ! docker secret ls | grep -q "changuapp_jwt_secret"; then
    print_error "Required secret 'changuapp_jwt_secret' not found. Run ./scripts/swarm-init.sh first."
    exit 1
fi

# Check if required configs exist
if ! docker config ls | grep -q "changuapp_nginx_config"; then
    print_error "Required config 'changuapp_nginx_config' not found. Run ./scripts/swarm-init.sh first."
    exit 1
fi

print_success "Pre-deployment checks passed!"

# Deploy the stack
deploy_service $DEPLOY_TARGET

# Post-deployment status
print_status "Deployment completed! Checking service status..."
echo ""

docker service ls --filter label=com.docker.stack.namespace=$STACK_NAME

echo ""
print_status "Service details:"
docker stack ps $STACK_NAME --no-trunc

echo ""
print_status "Health checks:"
for service in postgres backend frontend proxy; do
    if docker service ls --filter name="${STACK_NAME}_${service}" --format "{{.Name}}" | grep -q "${service}"; then
        check_service_health "${STACK_NAME}_${service}" || true
    fi
done

echo ""
print_success "Deployment completed! 🎉"
echo ""
print_status "Useful commands:"
echo "• Check services: docker service ls"
echo "• View logs: docker service logs ${STACK_NAME}_backend"
echo "• Scale service: docker service scale ${STACK_NAME}_backend=2"
echo "• Update service: docker service update ${STACK_NAME}_backend --image ${DOCKER_REGISTRY}/backend:${IMAGE_TAG}"
echo "• Remove stack: docker stack rm ${STACK_NAME}"
echo ""
print_status "Access your application:"
echo "• Frontend: http://$(curl -s http://***************/latest/meta-data/public-ipv4)/"
echo "• API: http://$(curl -s http://***************/latest/meta-data/public-ipv4):8080/api/"
echo "• Health: http://$(curl -s http://***************/latest/meta-data/public-ipv4):8080/health"
