#!/bin/bash

echo "🧹 Cleaning up native PostgreSQL to use Docker PostgreSQL..."

# Stop PostgreSQL service
echo "Stopping PostgreSQL service..."
sudo systemctl stop postgresql || echo "PostgreSQL service not running"

# Disable PostgreSQL service
echo "Disabling PostgreSQL service..."
sudo systemctl disable postgresql || echo "PostgreSQL service not enabled"

# Check if port 5432 is still in use
echo "Checking port 5432..."
sudo netstat -tlnp | grep :5432 || echo "Port 5432 is free"

# Kill any processes using port 5432
echo "Killing any processes using port 5432..."
sudo fuser -k 5432/tcp || echo "No processes using port 5432"

# Remove PostgreSQL packages (optional - uncomment if needed)
# echo "Removing PostgreSQL packages..."
# sudo yum remove -y postgresql* || echo "No PostgreSQL packages to remove"

echo "✅ Native PostgreSQL cleanup completed!"
echo "You can now run Docker Compose without port conflicts."
